"""
Utility functions for handling LLM parameters across different providers.
"""

def build_completion_params(
    model: str,
    provider: str,
    messages,
    temperature: float,
    top_p: float | None = None,
    top_k: int | None = None,
    tools=None,
    client=None,
    **kwargs
) -> dict:
    """
    Build completion parameters for litellm, handling provider-specific parameter support.
    
    Args:
        model: Model name
        provider: Provider name (openai, anthropic, etc.)
        messages: Messages for the completion
        temperature: Temperature parameter
        top_p: Top-p parameter (nucleus sampling)
        top_k: Top-k parameter (not supported by all providers via litellm)
        tools: Tools for function calling
        client: Custom client
        **kwargs: Additional parameters
    
    Returns:
        Dictionary of parameters safe to pass to litellm completion()
    """
    params = {
        "model": model,
        "custom_llm_provider": provider,
        "messages": messages,
        "temperature": temperature,
        **kwargs
    }
    
    if tools is not None:
        params["tools"] = tools
        
    if client is not None:
        params["client"] = client
    
    # Add top_p if supported and provided
    if top_p is not None:
        # Most providers support top_p via litellm
        params["top_p"] = top_p
    
    # Note: top_k is not widely supported via litellm for most providers
    # We skip it for litellm calls to avoid errors
    # For direct API calls (deployed mode), we handle top_k separately
    
    return params


def build_openai_api_params(
    model: str,
    messages,
    temperature: float,
    top_p: float | None = None,
    top_k: int | None = None,
    tools=None,
    **kwargs
) -> dict:
    """
    Build parameters for direct OpenAI API calls.
    
    Args:
        model: Model name
        messages: Messages for the completion
        temperature: Temperature parameter
        top_p: Top-p parameter
        top_k: Top-k parameter (passed via extra_body)
        tools: Tools for function calling
        **kwargs: Additional parameters
    
    Returns:
        Dictionary of parameters for OpenAI API
    """
    params = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        **kwargs
    }
    
    if tools is not None:
        params["tools"] = tools
    
    # Add top_p if provided
    if top_p is not None:
        params["top_p"] = top_p
    
    # For OpenAI API, top_k needs to be passed via extra_body
    if top_k is not None:
        params["extra_body"] = {"top_k": top_k}
    
    return params
